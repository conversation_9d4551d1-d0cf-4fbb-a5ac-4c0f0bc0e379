terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "~> 3.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_database_cluster" "example" {
  name = "db-ops-admin"
}

# HTTP data source to get detailed cluster information including connection details
data "http" "cluster_details" {
  url = "https://api.digitalocean.com/v2/databases/${data.digitalocean_database_cluster.example.id}"

  request_headers = {
    Authorization = "Bearer ${var.do_token}"
    Content-Type  = "application/json"
  }
}

# HTTP data source to get all connection pools for the database cluster
data "http" "database_connection_pools" {
  url = "https://api.digitalocean.com/v2/databases/${data.digitalocean_database_cluster.example.id}/pools"

  request_headers = {
    Authorization = "Bearer ${var.do_token}"
    Content-Type  = "application/json"
  }
}

# Parse the JSON responses
locals {
  # Cluster details
  cluster_response = jsondecode(data.http.cluster_details.response_body)
  cluster_info = local.cluster_response.database

  # Connection pools
  connection_pools_response = jsondecode(data.http.database_connection_pools.response_body)
  connection_pools = local.connection_pools_response.pools

  # Map cluster size to available backend connections
  # Based on DigitalOcean's plan specifications
  size_to_connections_map = {
    "db-s-1vcpu-1gb"   = 22   # 1 GiB RAM
    "db-s-1vcpu-2gb"   = 47   # 2 GiB RAM
    "db-s-2vcpu-4gb"   = 97   # 4 GiB RAM
    "db-s-4vcpu-8gb"   = 197  # 8 GiB RAM
    "db-s-8vcpu-16gb"  = 397  # 16 GiB RAM
    "db-s-8vcpu-32gb"  = 797  # 32 GiB RAM
    "db-s-16vcpu-64gb" = 997  # 64 GiB RAM
    # For 128+ GiB RAM plans, default to 997
    "db-s-32vcpu-128gb" = 997
    "db-s-64vcpu-256gb" = 997
  }

  # Get available backend connections based on cluster size
  available_backend_connections = lookup(local.size_to_connections_map, local.cluster_info.size, 997)

  # Calculate total connections used across all pools
  total_connections_used = sum([for pool in local.connection_pools : pool.size])

  # Calculate remaining available connections
  remaining_connections = local.available_backend_connections - local.total_connections_used

  # Extract connection availability information
  cluster_connection_info = {
    status = local.cluster_info.status
    connection = local.cluster_info.connection
    private_connection = try(local.cluster_info.private_connection, null)
    standby_connection = try(local.cluster_info.standby_connection, null)

    # Connection availability status
    is_available = local.cluster_info.status == "online"
    has_private_network = try(local.cluster_info.private_connection != null, false)
    has_standby = try(local.cluster_info.standby_connection != null, false)

    # Connection details
    host = local.cluster_info.connection.host
    port = local.cluster_info.connection.port
    user = local.cluster_info.connection.user
    database = local.cluster_info.connection.database
    ssl = local.cluster_info.connection.ssl
    uri = local.cluster_info.connection.uri

    # Private network details (if available)
    private_host = try(local.cluster_info.private_connection.host, null)
    private_port = try(local.cluster_info.private_connection.port, null)
    private_uri = try(local.cluster_info.private_connection.uri, null)

    # Standby connection details (if available)
    standby_host = try(local.cluster_info.standby_connection.host, null)
    standby_port = try(local.cluster_info.standby_connection.port, null)
    standby_uri = try(local.cluster_info.standby_connection.uri, null)
  }
}

# # Output cluster connection availability information
# output "cluster_connection_availability" {
#   description = "Cluster connection availability and details"
#   value = local.cluster_connection_info
# }
#
# output "cluster_status" {
#   description = "Current cluster status and availability"
#   value = {
#     status = local.cluster_info.status
#     is_online = local.cluster_info.status == "online"
#     created_at = local.cluster_info.created_at
#     region = local.cluster_info.region
#     size = local.cluster_info.size
#     num_nodes = local.cluster_info.num_nodes
#     engine = local.cluster_info.engine
#     version = local.cluster_info.version
#     available_backend_connections = local.available_backend_connections
#     total_connections_used = local.total_connections_used
#     remaining_connections = local.remaining_connections
#     connection_usage_percentage = floor((local.total_connections_used / local.available_backend_connections) * 10000) / 100
#   }
# }
#
# # Output the connection pools information
# output "connection_pools" {
#   description = "All connection pools in the database cluster"
#   value = {
#     pools = local.connection_pools
#     total_count = length(local.connection_pools)
#   }
# }

# # Output individual pool details for easier access
# output "connection_pool_names" {
#   description = "List of connection pool names"
#   value = [for pool in local.connection_pools : pool.name]
# }
#
# output "connection_pool_details" {
#   description = "Detailed information about each connection pool"
#   value = {
#     for pool in local.connection_pools : pool.name => {
#       mode = pool.mode
#       size = pool.size
#       db   = pool.db
#       user = pool.user
#       connection = pool.connection
#     }
#   }
# }

# Output connection usage summary
output "connection_usage_summary" {
  description = "Summary of connection pool usage across the cluster"
  value = {
    total_available_connections = local.available_backend_connections
    total_connections_used = local.total_connections_used
    remaining_connections = local.remaining_connections
    usage_percentage = floor((local.total_connections_used / local.available_backend_connections) * 10000) / 100
    pool_breakdown = {
      for pool in local.connection_pools : pool.name => {
        connections_used = pool.size
        mode = pool.mode
        database = pool.db
        user = pool.user
      }
    }
  }
}

variable "selected_pool_name" {
  type = string
  default = "ops-admin-pool"
}
